import { useState } from 'react';
import { Link } from 'react-router-dom';

const PurchaseHistory = () => {
  const [purchases] = useState([
    {
      id: 1,
      title: 'E-commerce React Template',
      seller: '<PERSON>',
      price: 299,
      purchaseDate: '2024-01-15',
      status: 'completed',
      downloadUrl: '#'
    },
    {
      id: 2,
      title: 'Dashboard UI Kit',
      seller: '<PERSON>',
      price: 199,
      purchaseDate: '2024-01-10',
      status: 'completed',
      downloadUrl: '#'
    },
    {
      id: 3,
      title: 'Landing Page Components',
      seller: '<PERSON>',
      price: 149,
      purchaseDate: '2024-01-05',
      status: 'completed',
      downloadUrl: '#'
    }
  ]);

  const totalSpent = purchases.reduce((sum, purchase) => sum + purchase.price, 0);

  return (
    <div className="dashboard-container min-h-screen">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-4xl font-black text-slate-900 tracking-tight">Purchase History</h1>
              <p className="text-lg text-slate-600 mt-2">Manage your purchased projects and downloads</p>
            </div>
            <div className="mt-4 sm:mt-0">
              <Link
                to="/marketplace"
                className="btn-primary btn-lg group"
              >
                <svg className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                Browse More Projects
              </Link>
            </div>
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="glass-card p-6 group hover:-translate-y-1 transition-all">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Total Purchases</p>
                <p className="text-2xl font-bold text-slate-900">{purchases.length}</p>
              </div>
            </div>
          </div>

          <div className="glass-card p-6 group hover:-translate-y-1 transition-all">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center group-hover:bg-green-200 transition-colors">
                <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Total Spent</p>
                <p className="text-2xl font-bold text-slate-900">${totalSpent}</p>
              </div>
            </div>
          </div>

          <div className="glass-card p-6 group hover:-translate-y-1 transition-all">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center group-hover:bg-purple-200 transition-colors">
                <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-slate-500">Available Downloads</p>
                <p className="text-2xl font-bold text-slate-900">{purchases.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Purchases Table */}
        <div className="glass-card overflow-hidden">
          <div className="px-6 py-4 border-b border-slate-200">
            <h2 className="text-xl font-bold text-slate-900">Your Purchases</h2>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-slate-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider">
                    Project
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider">
                    Seller
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider">
                    Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider">
                    Purchase Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-semibold text-slate-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-slate-200">
                {purchases.map((purchase) => (
                  <tr key={purchase.id} className="hover:bg-slate-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-slate-900">{purchase.title}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-slate-900">{purchase.seller}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-semibold text-slate-900">${purchase.price}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-slate-900">{purchase.purchaseDate}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex space-x-2">
                        <a
                          href={purchase.downloadUrl}
                          className="btn-primary btn-sm"
                        >
                          Download
                        </a>
                        <Link
                          to={`/project/${purchase.id}`}
                          className="btn-secondary btn-sm"
                        >
                          View Details
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PurchaseHistory;
