@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Modern SaaS Color Palette */
  --color-slate-900: #37474F;
  --color-slate-700: #546E7A;
  --color-slate-500: #90A4AE;
  --color-slate-300: #B0BEC5;
  --color-slate-100: #CFD8DC;
  --color-white: #FFFFFF;
  --color-gray-50: #F8FAFC;
  --color-gray-100: #F1F5F9;
  --color-gray-200: #E2E8F0;

  /* Accent colors */
  --color-blue-600: #2563EB;
  --color-blue-500: #3B82F6;
  --color-green-500: #10B981;
  --color-red-500: #EF4444;
  --color-yellow-500: #F59E0B;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

body {
  font-family: 'Inter', system-ui, sans-serif;
  background: var(--color-gray-50);
  color: var(--color-slate-900);
  line-height: 1.6;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  text-decoration: none;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.025em;
  box-shadow: 0 4px 14px 0 rgba(0, 0, 0, 0.1);
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.btn:disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}

.btn-primary {
  background: var(--color-slate-900);
  color: var(--color-white);
  padding: 0.75rem 1.5rem;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  background: var(--color-slate-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--color-white);
  color: var(--color-slate-900);
  border: 1px solid var(--color-slate-300);
  padding: 0.75rem 1.5rem;
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--color-gray-50);
  border-color: var(--color-slate-500);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  border: 2px solid var(--color-slate-300);
  background: transparent;
  color: var(--color-slate-700);
  padding: 0.75rem 1.5rem;
}

.btn-outline:hover {
  background: var(--color-slate-100);
  border-color: var(--color-slate-500);
  color: var(--color-slate-900);
  transform: translateY(-1px);
}

.btn-sm {
  height: 2rem;
  padding: 0 0.75rem;
  font-size: 0.75rem;
}

.btn-md {
  height: 2.5rem;
  padding: 0 1rem;
  font-size: 0.875rem;
}

.btn-lg {
  height: 3rem;
  padding: 0 1.5rem;
  font-size: 1rem;
}

/* Input styles */
.input {
  display: flex;
  height: 3rem;
  width: 100%;
  border-radius: 8px;
  border: 1px solid var(--color-slate-300);
  background: var(--color-white);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.input::placeholder {
  color: var(--color-slate-500);
}

.input:focus {
  outline: none;
  border-color: var(--color-slate-700);
  box-shadow: 0 0 0 3px rgba(55, 71, 79, 0.1);
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: var(--color-gray-100);
}

/* Card styles */
.card {
  border-radius: 12px;
  border: 1px solid var(--color-slate-200);
  background: var(--color-white);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-slate-300);
}

/* Badge styles */
.badge {
  display: inline-flex;
  align-items: center;
  border-radius: 6px;
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: 0.025em;
  transition: all 0.2s ease-in-out;
}

.badge-primary {
  background: var(--color-slate-100);
  color: var(--color-slate-900);
  border: 1px solid var(--color-slate-300);
}

.badge-secondary {
  background: var(--color-gray-100);
  color: var(--color-slate-700);
  border: 1px solid var(--color-slate-200);
}

.badge-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--color-green-500);
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--color-yellow-500);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge-error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--color-red-500);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Loading spinner */
.loading-spinner {
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.inline-flex { display: inline-flex; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.flex-1 { flex: 1 1 0%; }
.flex-col { flex-direction: column; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.min-h-screen { min-height: 100vh; }

.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }
.py-24 { padding-top: 6rem; padding-bottom: 6rem; }

.m-0 { margin: 0; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }

.space-x-4 > * + * { margin-left: 1rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }

.bg-white {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.bg-gray-50 {
  background: rgba(249, 250, 251, 0.8);
  backdrop-filter: blur(10px);
}
.bg-gray-100 {
  background: rgba(243, 244, 246, 0.9);
  backdrop-filter: blur(15px);
}

/* Modern containers */
.glass-container {
  background: var(--color-white);
  border: 1px solid var(--color-slate-200);
  border-radius: 12px;
  box-shadow: var(--shadow-md);
}

.glass-card {
  background: var(--color-white);
  border: 1px solid var(--color-slate-200);
  border-radius: 12px;
  box-shadow: var(--shadow-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.glass-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-slate-300);
}

/* Hero section styles */
.hero-gradient {
  background: linear-gradient(135deg, var(--color-slate-900) 0%, var(--color-slate-700) 100%);
}

/* Navbar styles */
.navbar-glass {
  background: var(--color-white);
  border-bottom: 1px solid var(--color-slate-200);
  box-shadow: var(--shadow-sm);
}

.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }

.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

.border { border-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }

.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }

.max-w-7xl { max-width: 80rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }

@media (min-width: 768px) {
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:text-4xl { font-size: 2.25rem; }
  .md\\:text-6xl { font-size: 3.75rem; }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
}

.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

.transition-colors { transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out; }
.transition-shadow { transition: box-shadow 0.2s ease-in-out; }

.hover\\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); }
.hover\\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); }

/* Additional modern utilities */
.bg-gradient-to-r { background: linear-gradient(to right, var(--tw-gradient-stops)); }
.bg-gradient-to-br { background: linear-gradient(to bottom right, var(--tw-gradient-stops)); }
.from-white { --tw-gradient-from: #ffffff; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(255, 255, 255, 0)); }
.to-purple-200 { --tw-gradient-to: #e9d5ff; }
.from-purple-600 { --tw-gradient-from: #9333ea; --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(147, 51, 234, 0)); }
.to-blue-600 { --tw-gradient-to: #2563eb; }

.bg-clip-text { -webkit-background-clip: text; background-clip: text; }
.text-transparent { color: transparent; }

.drop-shadow-lg { filter: drop-shadow(0 10px 8px rgba(0, 0, 0, 0.04)) drop-shadow(0 4px 3px rgba(0, 0, 0, 0.1)); }

.blur-3xl { filter: blur(64px); }

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.sticky { position: sticky; }
.top-0 { top: 0; }
.z-50 { z-index: 50; }

.relative { position: relative; }
.absolute { position: absolute; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }
.overflow-hidden { overflow: hidden; }

.-top-40 { top: -10rem; }
.-right-40 { right: -10rem; }
.-bottom-40 { bottom: -10rem; }
.-left-40 { left: -10rem; }

.w-80 { width: 20rem; }
.h-80 { height: 20rem; }
.w-72 { width: 18rem; }
.h-72 { height: 18rem; }
.w-20 { width: 5rem; }
.h-20 { height: 5rem; }
.w-16 { width: 4rem; }
.h-16 { height: 4rem; }
.w-10 { width: 2.5rem; }
.h-10 { height: 2.5rem; }
.w-5 { width: 1.25rem; }
.h-5 { height: 1.25rem; }

.opacity-10 { opacity: 0.1; }
.opacity-80 { opacity: 0.8; }

.text-white-90 { color: rgba(255, 255, 255, 0.9); }
.text-white-80 { color: rgba(255, 255, 255, 0.8); }

.leading-tight { line-height: 1.25; }
.leading-relaxed { line-height: 1.625; }

.font-black { font-weight: 900; }

.transform { transform: translateX(0) translateY(0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1); }
.scale-110 { transform: scale(1.1); }

.group:hover .group-hover\\:scale-110 { transform: scale(1.1); }
.group:hover .group-hover\\:text-purple-600 { color: #9333ea; }

.duration-300 { transition-duration: 300ms; }

.px-10 { padding-left: 2.5rem; padding-right: 2.5rem; }
.px-12 { padding-left: 3rem; padding-right: 3rem; }
.py-32 { padding-top: 8rem; padding-bottom: 8rem; }
.py-24 { padding-top: 6rem; padding-bottom: 6rem; }

.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }
.mb-20 { margin-bottom: 5rem; }
.mt-16 { margin-top: 4rem; }

.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }

.text-5xl { font-size: 3rem; }
.text-6xl { font-size: 3.75rem; }
.text-7xl { font-size: 4.5rem; }

@media (min-width: 768px) {
  .md\\:text-5xl { font-size: 3rem; }
  .md\\:text-6xl { font-size: 3.75rem; }
  .md\\:text-7xl { font-size: 4.5rem; }
}
