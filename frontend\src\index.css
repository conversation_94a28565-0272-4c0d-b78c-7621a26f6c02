@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Professional SaaS Color Palette */
  --slate-900: #37474F;
  --slate-700: #546E7A;
  --slate-500: #90A4AE;
  --slate-300: #B0BEC5;
  --slate-100: #CFD8DC;
  --white: #FFFFFF;
  --gray-50: #FAFBFC;
  --gray-100: #F4F6F8;
  --gray-200: #E8EAED;

  /* Accent colors */
  --blue-600: #1976D2;
  --blue-500: #2196F3;
  --green-500: #4CAF50;
  --red-500: #F44336;
  --yellow-500: #FF9800;
  --purple-500: #9C27B0;

  /* Modern shadows */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Border radius */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-2xl: 24px;

  /* Spacing */
  --space-xs: 4px;
  --space-sm: 8px;
  --space-md: 16px;
  --space-lg: 24px;
  --space-xl: 32px;
  --space-2xl: 48px;
  --space-3xl: 64px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--slate-100);
  color: var(--slate-900);
  line-height: 1.6;
  min-height: 100vh;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

/* Modern Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: none;
  text-decoration: none;
  position: relative;
  white-space: nowrap;
  user-select: none;
  outline: none;
  font-family: inherit;
  letter-spacing: -0.025em;
}

.btn:focus-visible {
  outline: 2px solid var(--blue-500);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Button Sizes */
.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.75rem;
  border-radius: var(--radius-md);
  height: 2rem;
}

.btn-md {
  padding: 0.625rem 1.25rem;
  font-size: 0.875rem;
  height: 2.5rem;
}

.btn-lg {
  padding: 0.875rem 1.75rem;
  font-size: 1rem;
  border-radius: var(--radius-xl);
  height: 3rem;
}

.btn-xl {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  border-radius: var(--radius-xl);
  height: 3.5rem;
}

/* Primary Button - Main CTA */
.btn-primary {
  background: var(--slate-900);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  background: var(--slate-700);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Secondary Button - Alternative action */
.btn-secondary {
  background: var(--white);
  color: var(--slate-900);
  border: 1px solid var(--slate-300);
  box-shadow: var(--shadow-xs);
}

.btn-secondary:hover {
  background: var(--gray-50);
  border-color: var(--slate-500);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

/* Outline Button - Subtle action */
.btn-outline {
  background: transparent;
  color: var(--slate-700);
  border: 1.5px solid var(--slate-300);
}

.btn-outline:hover {
  background: var(--slate-100);
  border-color: var(--slate-500);
  color: var(--slate-900);
}

/* Ghost Button - Minimal action */
.btn-ghost {
  background: transparent;
  color: var(--slate-700);
  border: none;
}

.btn-ghost:hover {
  background: var(--slate-100);
  color: var(--slate-900);
}

/* Danger Button */
.btn-danger {
  background: var(--red-500);
  color: var(--white);
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
  background: #d32f2f;
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

/* Modern Input System */
.input {
  display: flex;
  height: 3rem;
  width: 100%;
  border-radius: var(--radius-lg);
  border: 1px solid var(--slate-300);
  background: var(--white);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
  font-family: inherit;
}

.input::placeholder {
  color: var(--slate-500);
}

.input:focus {
  outline: none;
  border-color: var(--slate-700);
  box-shadow: 0 0 0 3px rgba(55, 71, 79, 0.1);
}

.input:disabled {
  cursor: not-allowed;
  opacity: 0.6;
  background: var(--gray-100);
}

/* Modern Card System */
.card {
  border-radius: var(--radius-xl);
  border: 1px solid var(--slate-300);
  background: var(--white);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--slate-500);
}

/* Glass Card - Premium elevated card */
.glass-card {
  background: var(--white);
  border: 1px solid var(--slate-300);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
}

.glass-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--slate-500);
}

/* Glass Container - Subtle elevated container */
.glass-container {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid var(--slate-300);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(12px);
}

/* Modern Badge System */
.badge {
  display: inline-flex;
  align-items: center;
  border-radius: var(--radius-md);
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 500;
  letter-spacing: -0.025em;
  transition: all 0.2s ease-in-out;
  border: 1px solid transparent;
}

.badge-primary {
  background: var(--slate-100);
  color: var(--slate-900);
  border-color: var(--slate-300);
}

.badge-secondary {
  background: var(--gray-100);
  color: var(--slate-700);
  border-color: var(--slate-300);
}

.badge-success {
  background: rgba(76, 175, 80, 0.1);
  color: var(--green-500);
  border-color: rgba(76, 175, 80, 0.2);
}

.badge-warning {
  background: rgba(255, 152, 0, 0.1);
  color: var(--yellow-500);
  border-color: rgba(255, 152, 0, 0.2);
}

.badge-error {
  background: rgba(244, 67, 54, 0.1);
  color: var(--red-500);
  border-color: rgba(244, 67, 54, 0.2);
}

/* Layout Styles */
.hero-gradient {
  background: linear-gradient(135deg, var(--slate-900) 0%, var(--slate-700) 100%);
}

.navbar-glass {
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid var(--slate-300);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(12px);
}

/* Dashboard Layout */
.dashboard-container {
  background: var(--gray-50);
  min-height: 100vh;
}

.dashboard-sidebar {
  background: var(--white);
  border-right: 1px solid var(--slate-300);
  box-shadow: var(--shadow-sm);
}

.dashboard-main {
  background: var(--gray-50);
  padding: var(--space-lg);
}

/* Loading spinner */
.loading-spinner {
  border: 2px solid var(--slate-300);
  border-top: 2px solid var(--slate-700);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modern Utility Classes */
.text-slate-900 { color: var(--slate-900); }
.text-slate-700 { color: var(--slate-700); }
.text-slate-500 { color: var(--slate-500); }
.text-slate-300 { color: var(--slate-300); }
.text-white { color: var(--white); }

.bg-slate-900 { background: var(--slate-900); }
.bg-slate-700 { background: var(--slate-700); }
.bg-slate-500 { background: var(--slate-500); }
.bg-slate-300 { background: var(--slate-300); }
.bg-slate-100 { background: var(--slate-100); }
.bg-white { background: var(--white); }
.bg-gray-50 { background: var(--gray-50); }
.bg-gray-100 { background: var(--gray-100); }

.border-slate-300 { border-color: var(--slate-300); }
.border-slate-500 { border-color: var(--slate-500); }

.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: 9999px; }

/* Typography */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0em; }
.tracking-wide { letter-spacing: 0.025em; }

/* Layout & Display */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.inline-flex { display: inline-flex; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }
.grid { display: grid; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.flex-1 { flex: 1 1 0%; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }

/* Sizing */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.min-h-screen { min-height: 100vh; }

/* Spacing */
.p-0 { padding: 0; }
.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.p-12 { padding: 3rem; }

.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }

.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.py-20 { padding-top: 5rem; padding-bottom: 5rem; }
.py-24 { padding-top: 6rem; padding-bottom: 6rem; }

.m-0 { margin: 0; }
.m-auto { margin: auto; }
.mx-auto { margin-left: auto; margin-right: auto; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-12 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-6 > * + * { margin-left: 1.5rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.space-y-8 > * + * { margin-top: 2rem; }

/* Grid System */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-12 { gap: 3rem; }

/* Container */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

.max-w-sm { max-width: 24rem; }
.max-w-md { max-width: 28rem; }
.max-w-lg { max-width: 32rem; }
.max-w-xl { max-width: 36rem; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-4xl { max-width: 56rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-6xl { max-width: 72rem; }
.max-w-7xl { max-width: 80rem; }

/* Position */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* Overflow */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }

/* Transitions */
.transition-all { transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); }
.transition-colors { transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out; }
.transition-transform { transition: transform 0.2s ease-in-out; }
.transition-shadow { transition: box-shadow 0.2s ease-in-out; }

/* Transform */
.transform { transform: translateX(0) translateY(0) rotate(0) skewX(0) skewY(0) scaleX(1) scaleY(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.-translate-y-1 { transform: translateY(-0.25rem); }
.-translate-y-2 { transform: translateY(-0.5rem); }

/* Hover Effects */
.hover\\:scale-105:hover { transform: scale(1.05); }
.hover\\:scale-110:hover { transform: scale(1.1); }
.hover\\:-translate-y-1:hover { transform: translateY(-0.25rem); }
.hover\\:-translate-y-2:hover { transform: translateY(-0.5rem); }
.hover\\:shadow-lg:hover { box-shadow: var(--shadow-lg); }
.hover\\:shadow-xl:hover { box-shadow: var(--shadow-xl); }

/* Group Hover */
.group:hover .group-hover\\:scale-105 { transform: scale(1.05); }
.group:hover .group-hover\\:scale-110 { transform: scale(1.1); }
.group:hover .group-hover\\:-translate-y-1 { transform: translateY(-0.25rem); }
.group:hover .group-hover\\:bg-slate-200 { background: var(--slate-300); }

/* Responsive Design */
@media (min-width: 640px) {
  .sm\\:flex-row { flex-direction: row; }
  .sm\\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
  .sm\\:text-lg { font-size: 1.125rem; }
  .sm\\:text-xl { font-size: 1.25rem; }
}

@media (min-width: 768px) {
  .md\\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\\:flex-row { flex-direction: row; }
  .md\\:text-lg { font-size: 1.125rem; }
  .md\\:text-xl { font-size: 1.25rem; }
  .md\\:text-2xl { font-size: 1.5rem; }
  .md\\:text-3xl { font-size: 1.875rem; }
  .md\\:text-4xl { font-size: 2.25rem; }
  .md\\:text-5xl { font-size: 3rem; }
  .md\\:text-6xl { font-size: 3.75rem; }
}

@media (min-width: 1024px) {
  .lg\\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\\:px-8 { padding-left: 2rem; padding-right: 2rem; }
  .lg\\:text-xl { font-size: 1.25rem; }
  .lg\\:text-2xl { font-size: 1.5rem; }
}

@media (min-width: 1280px) {
  .xl\\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .xl\\:grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
}

/* Additional Utilities */
.w-4 { width: 1rem; }
.w-5 { width: 1.25rem; }
.w-6 { width: 1.5rem; }
.w-8 { width: 2rem; }
.w-10 { width: 2.5rem; }
.w-12 { width: 3rem; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.w-24 { width: 6rem; }
.w-32 { width: 8rem; }

.h-4 { height: 1rem; }
.h-5 { height: 1.25rem; }
.h-6 { height: 1.5rem; }
.h-8 { height: 2rem; }
.h-10 { height: 2.5rem; }
.h-12 { height: 3rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-24 { height: 6rem; }
.h-32 { height: 8rem; }

.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.select-none { user-select: none; }

.border { border-width: 1px; }
.border-0 { border-width: 0; }
.border-2 { border-width: 2px; }

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
}

/* Focus States */
.focus\\:outline-none:focus { outline: none; }
.focus\\:ring-2:focus { box-shadow: 0 0 0 2px var(--blue-500); }
.focus\\:ring-offset-2:focus { box-shadow: 0 0 0 2px var(--white), 0 0 0 4px var(--blue-500); }

/* Backdrop Blur */
.backdrop-blur-sm { backdrop-filter: blur(4px); }
.backdrop-blur { backdrop-filter: blur(8px); }
.backdrop-blur-md { backdrop-filter: blur(12px); }
.backdrop-blur-lg { backdrop-filter: blur(16px); }

/* Modern Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}
